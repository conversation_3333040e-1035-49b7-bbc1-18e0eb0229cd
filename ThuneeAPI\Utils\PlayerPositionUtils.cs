using ThuneeAPI.Models;

namespace ThuneeAPI.Utils
{
    /// <summary>
    /// Player position utilities for Thunee
    /// 
    /// In Thunee, we use fixed positions 1-4 where:
    /// - Position 1: Top left player
    /// - Position 2: Top right player
    /// - Position 3: Bottom left player (dealer)
    /// - Position 4: Bottom right player
    /// 
    /// Team 1 consists of positions 1 and 3
    /// Team 2 consists of positions 2 and 4
    /// 
    /// Play moves counter-clockwise, so the player to the right of position X
    /// is position (X % 4) + 1
    /// </summary>
    public static class PlayerPositionUtils
    {
        /// <summary>
        /// Get the next player position in counter-clockwise order
        /// 
        /// According to the specific rule set:
        /// Position 1: Player 1 (Team A)
        /// Position 2: Player 3 (Team B)
        /// Position 3: Player 2 (Team A)
        /// Position 4: Player 4 (Team B)
        /// 
        /// Play order:
        /// - If Player 1 (position 1) plays first → Player 3 (position 2) → Player 2 (position 3) → Player 4 (position 4)
        /// - If Player 3 (position 2) plays first → Player 2 (position 3) → Player 4 (position 4) → Player 1 (position 1)
        /// - If Player 2 (position 3) plays first → Player 4 (position 4) → Player 1 (position 1) → Player 3 (position 2)
        /// - If Player 4 (position 4) plays first → Player 1 (position 1) → Player 3 (position 2) → Player 2 (position 3)
        /// </summary>
        public static int GetPositionToRight(int position)
        {
            // Ensure position is a number between 1 and 4
            if (position < 1 || position > 4)
            {
                Console.WriteLine($"Invalid position: {position}");
                return 1; // Default to position 1 if invalid
            }

            // Map of positions to the next position in counter-clockwise order
            // Based on the specific rule set provided
            var nextPositions = new Dictionary<int, int>
            {
                { 1, 2 }, // From Player 1 (position 1) to Player 3 (position 2)
                { 2, 3 }, // From Player 3 (position 2) to Player 2 (position 3)
                { 3, 4 }, // From Player 2 (position 3) to Player 4 (position 4)
                { 4, 1 }  // From Player 4 (position 4) to Player 1 (position 1)
            };

            Console.WriteLine($"Getting next position after {position}: {nextPositions[position]}");
            return nextPositions[position];
        }

        /// <summary>
        /// Get the previous player position in clockwise order
        /// 
        /// According to the specific rule set:
        /// Position 1: Player 1 (Team A)
        /// Position 2: Player 3 (Team B)
        /// Position 3: Player 2 (Team A)
        /// Position 4: Player 4 (Team B)
        /// 
        /// Reverse play order:
        /// - Position 1 (Player 1) -> Position 4 (Player 4) is previous
        /// - Position 2 (Player 3) -> Position 1 (Player 1) is previous
        /// - Position 3 (Player 2) -> Position 2 (Player 3) is previous
        /// - Position 4 (Player 4) -> Position 3 (Player 2) is previous
        /// </summary>
        public static int GetPositionToLeft(int position)
        {
            // Ensure position is a number between 1 and 4
            if (position < 1 || position > 4)
            {
                Console.WriteLine($"Invalid position: {position}");
                return 1; // Default to position 1 if invalid
            }

            // Map of positions to the previous position in clockwise order
            // Based on the specific rule set provided
            var previousPositions = new Dictionary<int, int>
            {
                { 1, 4 }, // From Player 1 (position 1) to Player 4 (position 4)
                { 2, 1 }, // From Player 3 (position 2) to Player 1 (position 1)
                { 3, 2 }, // From Player 2 (position 3) to Player 3 (position 2)
                { 4, 3 }  // From Player 4 (position 4) to Player 2 (position 3)
            };

            Console.WriteLine($"Getting previous position before {position}: {previousPositions[position]}");
            return previousPositions[position];
        }

        /// <summary>
        /// Get the position of the partner player
        /// 
        /// According to the specific rule set:
        /// - Position 1 (Player 1, Team A) is partnered with Position 3 (Player 2, Team A)
        /// - Position 2 (Player 3, Team B) is partnered with Position 4 (Player 4, Team B)
        /// - Position 3 (Player 2, Team A) is partnered with Position 1 (Player 1, Team A)
        /// - Position 4 (Player 4, Team B) is partnered with Position 2 (Player 3, Team B)
        /// </summary>
        public static int GetOppositePosition(int position)
        {
            // Ensure position is a number between 1 and 4
            if (position < 1 || position > 4)
            {
                Console.WriteLine($"Invalid position: {position}");
                return 1; // Default to position 1 if invalid
            }

            // Map of positions to their partner positions
            var partnerPositions = new Dictionary<int, int>
            {
                { 1, 3 }, // Player 1 (Team A) <-> Player 2 (Team A)
                { 2, 4 }, // Player 3 (Team B) <-> Player 4 (Team B)
                { 3, 1 }, // Player 2 (Team A) <-> Player 1 (Team A)
                { 4, 2 }  // Player 4 (Team B) <-> Player 3 (Team B)
            };

            Console.WriteLine($"Getting partner position for {position}: {partnerPositions[position]}");
            return partnerPositions[position];
        }

        /// <summary>
        /// Get the team number (1 or 2) for a given position
        /// 
        /// According to the specific rule set:
        /// - Team A (1): positions 1 and 3 (Player 1 and Player 2)
        /// - Team B (2): positions 2 and 4 (Player 3 and Player 4)
        /// </summary>
        public static int GetTeamForPosition(int position)
        {
            // Team A (1): positions 1 and 3 (Player 1 and Player 2)
            // Team B (2): positions 2 and 4 (Player 3 and Player 4)
            var teamMap = new Dictionary<int, int>
            {
                { 1, 1 }, // Player 1 - Team A
                { 2, 2 }, // Player 3 - Team B
                { 3, 1 }, // Player 2 - Team A
                { 4, 2 }  // Player 4 - Team B
            };

            return teamMap.ContainsKey(position) ? teamMap[position] : 1; // Default to team 1 if invalid
        }

        /// <summary>
        /// Get the partner position for a given position
        /// 
        /// According to the specific rule set:
        /// - Position 1 (Player 1, Team A) is partnered with Position 3 (Player 2, Team A)
        /// - Position 2 (Player 3, Team B) is partnered with Position 4 (Player 4, Team B)
        /// - Position 3 (Player 2, Team A) is partnered with Position 1 (Player 1, Team A)
        /// - Position 4 (Player 4, Team B) is partnered with Position 2 (Player 3, Team B)
        /// </summary>
        public static int GetPartnerPosition(int position)
        {
            // This is the same as getting the opposite position
            return GetOppositePosition(position);
        }

        /// <summary>
        /// Get the dealer position (can be any position 1-4)
        /// This is a placeholder as the dealer can be any player
        /// </summary>
        public static int GetDealerPosition()
        {
            // In this implementation, the dealer can be any player
            // The actual dealer is determined during the game
            return 3; // Default to position 3 for compatibility
        }

        /// <summary>
        /// Get the first player position after trump selection
        /// 
        /// According to the specific rule set:
        /// - If Player 1 (position 1) selects trump → Player 3 (position 2) plays first
        /// - If Player 2 (position 3) selects trump → Player 4 (position 4) plays first
        /// - If Player 3 (position 2) selects trump → Player 2 (position 3) plays first
        /// - If Player 4 (position 4) selects trump → Player 1 (position 1) plays first
        /// </summary>
        /// <param name="trumperPosition">The position of the player who selected trump</param>
        /// <returns>The position of the player who should play first</returns>
        public static int GetFirstPlayerAfterTrump(int trumperPosition)
        {
            // Ensure position is a number between 1 and 4
            if (trumperPosition < 1 || trumperPosition > 4)
            {
                Console.WriteLine($"Invalid trumper position: {trumperPosition}");
                return 1; // Default to position 1 if invalid
            }

            // Map of trumper positions to first player positions
            var firstPlayerMap = new Dictionary<int, int>
            {
                { 1, 2 }, // If Player 1 (position 1) selects trump → Player 3 (position 2) plays first
                { 3, 4 }, // If Player 2 (position 3) selects trump → Player 4 (position 4) plays first
                { 2, 3 }, // If Player 3 (position 2) selects trump → Player 2 (position 3) plays first
                { 4, 1 }  // If Player 4 (position 4) selects trump → Player 1 (position 1) plays first
            };

            Console.WriteLine($"Getting first player after trump selection by player at position {trumperPosition}: {firstPlayerMap[trumperPosition]}");
            return firstPlayerMap[trumperPosition];
        }

        /// <summary>
        /// Get the initial trumper position based on the dealer position
        /// 
        /// In Thunee, the player to the right of the dealer selects trump.
        /// Since the dealer is always at position 3 (bottom left), the trumper
        /// is always at position 2 (top right).
        /// </summary>
        /// <param name="dealerPosition">The position of the dealer (should always be 3)</param>
        /// <returns>The position of the initial trumper (always 2)</returns>
        public static int GetInitialTrumperPosition(int dealerPosition)
        {
            // In our fixed position system, the dealer is always at position 3
            // and the trumper is always at position 2 (to the right of dealer)

            // Log the input for debugging
            Console.WriteLine($"Getting initial trumper position for dealer at position {dealerPosition}");

            // For backward compatibility, check if a position was provided
            if (dealerPosition >= 1 && dealerPosition <= 4)
            {
                // If a valid position was provided, use the map for backward compatibility
                var trumperMap = new Dictionary<int, int>
                {
                    { 1, 2 }, // If Player 1 is dealer → Player 3 (position 2) selects trump
                    { 2, 4 }, // If Player 2 is dealer → Player 4 (position 4) selects trump
                    { 3, 2 }, // If Player 3 is dealer → Player 2 (position 2) selects trump
                    { 4, 1 }  // If Player 4 is dealer → Player 1 (position 1) selects trump
                };

                Console.WriteLine($"Using compatibility map: trumper position for dealer at position {dealerPosition} is {trumperMap[dealerPosition]}");
                return trumperMap[dealerPosition];
            }

            // In the standard case, the dealer is at position 3 and the trumper is at position 2
            Console.WriteLine("Using standard position: trumper is at position 2 (top right)");
            return 2;
        }

        /// <summary>
        /// Assign fixed positions to players based on the dealer
        ///
        /// According to the specific rule set:
        /// - Position 3: Dealer (always bottom left)
        /// - Position 1: Dealer's partner (always top left)
        /// - Positions 2 and 4: Opposing team players (top right and bottom right)
        /// </summary>
        /// <param name="players">List of player objects</param>
        /// <param name="dealerId">ID of the dealer</param>
        /// <returns>List of players with positions assigned</returns>
        public static List<Player> AssignPositionsBasedOnDealer(List<Player> players, string dealerId)
        {
            if (string.IsNullOrEmpty(dealerId) || players.Count != 4)
            {
                Console.WriteLine("Cannot assign positions: Invalid dealer ID or player count");
                return players;
            }

            // Find the dealer
            var dealer = players.FirstOrDefault(p => p.Id == dealerId);
            if (dealer == null)
            {
                Console.WriteLine($"Dealer with ID {dealerId} not found in players list");
                return players;
            }

            // Get the dealer's team
            var dealerTeam = dealer.Team;
            if (dealerTeam == 0)
            {
                Console.WriteLine("Dealer has no team assigned");
                return players;
            }

            // Create a copy of the players list
            var positionedPlayers = new List<Player>(players);

            // Find the dealer's partner (same team, but not the dealer)
            var dealerPartner = players.FirstOrDefault(p => p.Team == dealerTeam && p.Id != dealerId);
            if (dealerPartner == null)
            {
                Console.WriteLine("Dealer partner not found");
                return players;
            }

            // Find the opposing team players
            var opposingTeam = dealerTeam == 1 ? 2 : 1;
            var opposingTeamPlayers = players.Where(p => p.Team == opposingTeam).ToList();
            if (opposingTeamPlayers.Count != 2)
            {
                Console.WriteLine("Invalid opposing team distribution");
                return players;
            }

            // Assign positions according to the specific rule set
            // Position 3: Dealer (bottom left)
            dealer.Position = 3;

            // Position 1: Dealer's partner (top left)
            dealerPartner.Position = 1;

            // Position 2: First opposing team player (top right)
            opposingTeamPlayers[0].Position = 2;

            // Position 4: Second opposing team player (bottom right)
            opposingTeamPlayers[1].Position = 4;

            Console.WriteLine("Assigned positions to players:");
            foreach (var player in positionedPlayers)
            {
                Console.WriteLine($"{player.Name} ({player.Id}) - Team {player.Team} - Position {player.Position}");
            }

            return positionedPlayers;
        }
    }
}
